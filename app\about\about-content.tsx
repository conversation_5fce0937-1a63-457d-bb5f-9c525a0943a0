"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Building2,
  Users,
  Award,
  Target,
  Eye,
  Heart,
  Shield,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  CheckCircle,
  Star,
  Home,
  Key,
  Calculator,
  Handshake,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function AboutContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-burgundy-900 via-burgundy-800 to-red-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/images/pattern.svg')] bg-repeat opacity-20"></div>
        </div>

        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/hero-bg.jpg"
            alt="About Dwelling Desire"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-32 h-32 bg-yellow-300/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-white/5 rounded-full blur-lg animate-pulse delay-500"></div>
        </div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm backdrop-blur-sm">
                <BookOpen size={16} className="mr-2" />
                About Us
              </Badge>

              <h1 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                About Dwelling Desire
              </h1>

              <p className="text-xl lg:text-2xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed">
                Your trusted partner in Ahmedabad's real estate journey. We transform property dreams into reality with expertise, transparency, and unwavering commitment to excellence.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-white text-burgundy-900 hover:bg-gray-100 px-8 py-3 rounded-full font-semibold transform hover:scale-105 transition-all duration-300 shadow-lg">
                  <Link href="/contact">Get In Touch</Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-burgundy-900 px-8 py-3 rounded-full font-semibold transform hover:scale-105 transition-all duration-300 backdrop-blur-sm">
                  <Link href="/properties">View Properties</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="py-20 bg-white relative overflow-hidden">
        {/* Background Decorations */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 right-10 w-64 h-64 bg-burgundy-50 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute bottom-20 left-10 w-48 h-48 bg-yellow-50 rounded-full blur-2xl opacity-40"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="max-w-4xl mx-auto">
            {/* Page Title */}
            <div className="text-center mb-16 animate-on-scroll opacity-0 translate-y-8">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Welcome to <span className="text-burgundy-600 bg-gradient-to-r from-burgundy-600 to-red-600 bg-clip-text text-transparent">Dwelling Desire</span>
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-burgundy-600 to-red-600 mx-auto rounded-full"></div>
            </div>

            {/* Company Overview */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-200 mb-16">
              <div className="bg-gradient-to-br from-white to-gray-50 p-8 rounded-2xl shadow-lg border border-gray-100">
                <div className="prose prose-lg max-w-none text-gray-600 leading-relaxed space-y-6">
                  <p className="text-xl leading-relaxed">
                    Founded in 2019, Dwelling Desire has emerged as <strong className="text-burgundy-600">Ahmedabad's premier real estate consultancy</strong>, specializing in residential and commercial properties across Gujarat's most sought-after locations.
                  </p>

                  <p className="text-lg">
                    With deep local market knowledge and a commitment to transparent dealings, we've successfully facilitated over <strong className="text-burgundy-600">500+ property transactions</strong>, helping families find their dream homes and investors discover lucrative opportunities.
                  </p>
                </div>
              </div>
            </div>

            {/* Mission & Vision */}
            <div className="grid md:grid-cols-2 gap-8 mb-16">
              <Card className="animate-on-scroll opacity-0 translate-y-8 delay-300 border-0 shadow-xl bg-gradient-to-br from-burgundy-50 to-red-50 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-burgundy-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To provide exceptional real estate services that exceed client expectations through transparent dealings, expert market knowledge, and personalized attention. We strive to make property transactions seamless, secure, and rewarding for every client.
                  </p>
                </CardContent>
              </Card>

              <Card className="animate-on-scroll opacity-0 translate-y-8 delay-400 border-0 shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To become Gujarat's most trusted and innovative real estate consultancy, setting new standards in customer service and market expertise. We envision a future where every property transaction is a stepping stone to our clients' success and happiness.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Services */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-500 mb-16">
              <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">Our <span className="text-burgundy-600">Services</span></h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  { icon: Home, title: "Property Buying", desc: "Expert guidance in finding and purchasing your dream property" },
                  { icon: Key, title: "Property Selling", desc: "Strategic marketing to achieve the best value for your investment" },
                  { icon: Handshake, title: "Leasing Services", desc: "Comprehensive rental solutions for landlords and tenants" },
                  { icon: Calculator, title: "Investment Consultation", desc: "Data-driven advice to maximize returns" },
                  { icon: Building2, title: "Property Management", desc: "End-to-end property management services" },
                  { icon: Shield, title: "Legal Support", desc: "Complete legal assistance and documentation" }
                ].map((service, index) => (
                  <div key={service.title} className={`bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group`}>
                    <div className="w-12 h-12 bg-burgundy-100 rounded-xl flex items-center justify-center mb-4 group-hover:bg-burgundy-200 transition-colors">
                      <service.icon className="w-6 h-6 text-burgundy-600" />
                    </div>
                    <h4 className="font-bold text-gray-900 mb-2">{service.title}</h4>
                    <p className="text-gray-600 text-sm">{service.desc}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Why Choose Us */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-600 mb-16">
              <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">Why Choose <span className="text-burgundy-600">Dwelling Desire?</span></h3>
              <div className="bg-gradient-to-br from-gray-50 to-white p-8 rounded-2xl shadow-lg border border-gray-100">
                <div className="grid md:grid-cols-2 gap-6">
                  {[
                    "RERA certified and legally compliant operations",
                    "Extensive network of verified properties and builders",
                    "End-to-end transaction support and documentation",
                    "Market analysis and investment advisory services",
                    "Post-sale support and property management assistance",
                    "Multilingual team fluent in Gujarati, Hindi, and English"
                  ].map((point, index) => (
                    <div key={index} className="flex items-start space-x-3 group">
                      <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform" />
                      <span className="text-gray-700 group-hover:text-gray-900 transition-colors">{point}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* CTA Section */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-700">
              <div className="bg-gradient-to-r from-burgundy-600 to-red-600 p-8 rounded-2xl shadow-2xl text-center">
                <h3 className="text-3xl font-bold text-white mb-4">Ready to Start Your Real Estate Journey?</h3>
                <p className="text-white/90 mb-6 text-lg">
                  Our expert team is here to help you achieve your property goals with personalized service and market expertise.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-burgundy-600 hover:bg-gray-100 px-8 py-3 rounded-full font-semibold transform hover:scale-105 transition-all duration-300 shadow-lg">
                    <Link href="/contact">Contact Us Today</Link>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-burgundy-600 px-8 py-3 rounded-full font-semibold transform hover:scale-105 transition-all duration-300">
                    <Link href="/properties">Explore Properties</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


    </main>
  )
}

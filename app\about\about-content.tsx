"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Building2, 
  Users, 
  Award, 
  Target, 
  Eye, 
  Heart,
  Shield,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  CheckCircle,
  Star,
  Home,
  Key,
  Calculator,
  Handshake
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function AboutContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-burgundy-900 via-burgundy-800 to-red-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/images/pattern.svg')] bg-repeat opacity-20"></div>
        </div>
        
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/hero-bg.jpg"
            alt="Dwelling Desire Office"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <Building2 size={16} className="mr-2" />
              Established 2019
            </Badge>

            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              About <span className="text-yellow-300">Dwelling Desire</span>
            </h1>

            <p className="text-xl lg:text-2xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed">
              Your trusted partner in Ahmedabad's real estate journey. We transform property dreams into reality with expertise, transparency, and unwavering commitment to excellence.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-burgundy-900 hover:bg-gray-100 px-8 py-3 rounded-full font-semibold">
                <Link href="/contact">Get In Touch</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-burgundy-900 px-8 py-3 rounded-full font-semibold">
                <Link href="/properties">View Properties</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="animate-on-scroll opacity-0 translate-y-8">
                <Badge className="bg-burgundy-100 text-burgundy-800 mb-6">
                  <Building2 size={16} className="mr-2" />
                  Our Story
                </Badge>
                
                <h2 className="text-4xl font-bold text-gray-900 mb-6">
                  Redefining Real Estate Excellence in <span className="text-burgundy-600">Ahmedabad</span>
                </h2>
                
                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                  Founded in 2019, Dwelling Desire has emerged as Ahmedabad's premier real estate consultancy, specializing in luxury residential and commercial properties across Gujarat's most sought-after locations.
                </p>
                
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  With deep local market knowledge and a commitment to transparent dealings, we've successfully facilitated over 500+ property transactions, helping families find their dream homes and investors discover lucrative opportunities.
                </p>

                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-4 bg-burgundy-50 rounded-xl">
                    <div className="text-3xl font-bold text-burgundy-600 mb-2">500+</div>
                    <div className="text-sm text-gray-600">Properties Sold</div>
                  </div>
                  <div className="text-center p-4 bg-burgundy-50 rounded-xl">
                    <div className="text-3xl font-bold text-burgundy-600 mb-2">5+</div>
                    <div className="text-sm text-gray-600">Years Experience</div>
                  </div>
                </div>
              </div>

              <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
                <div className="relative">
                  <Image
                    src="/images/about.webp"
                    alt="Dwelling Desire Office"
                    width={600}
                    height={400}
                    className="rounded-2xl shadow-2xl"
                  />
                  <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-xl shadow-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-burgundy-100 rounded-full flex items-center justify-center">
                        <Award className="w-6 h-6 text-burgundy-600" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">Award Winning</div>
                        <div className="text-sm text-gray-600">Real Estate Firm</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Our Mission & <span className="text-burgundy-600">Vision</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Driven by purpose, guided by values, and committed to excellence in every transaction.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-12">
              <Card className="animate-on-scroll opacity-0 translate-y-8 border-0 shadow-xl bg-white">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-burgundy-100 rounded-2xl flex items-center justify-center mb-6">
                    <Target className="w-8 h-8 text-burgundy-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To provide exceptional real estate services that exceed client expectations through transparent dealings, expert market knowledge, and personalized attention. We strive to make property transactions seamless, secure, and rewarding for every client.
                  </p>
                </CardContent>
              </Card>

              <Card className="animate-on-scroll opacity-0 translate-y-8 delay-200 border-0 shadow-xl bg-white">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-burgundy-100 rounded-2xl flex items-center justify-center mb-6">
                    <Eye className="w-8 h-8 text-burgundy-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
                  <p className="text-gray-600 leading-relaxed">
                    To become Gujarat's most trusted and innovative real estate consultancy, setting new standards in customer service and market expertise. We envision a future where every property transaction is a stepping stone to our clients' success and happiness.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Services Offered */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Our <span className="text-burgundy-600">Services</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive real estate solutions tailored to meet your unique property needs.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: Home,
                  title: "Property Buying",
                  description: "Expert guidance in finding and purchasing your dream property with complete legal assistance."
                },
                {
                  icon: Key,
                  title: "Property Selling",
                  description: "Strategic marketing and negotiation to achieve the best value for your property investment."
                },
                {
                  icon: Handshake,
                  title: "Leasing Services",
                  description: "Comprehensive rental solutions for both landlords and tenants with transparent processes."
                },
                {
                  icon: Calculator,
                  title: "Investment Consultation",
                  description: "Data-driven investment advice to maximize returns in Ahmedabad's growing real estate market."
                }
              ].map((service, index) => (
                <Card key={service.title} className={`animate-on-scroll opacity-0 translate-y-8 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group`} style={{ animationDelay: `${index * 100}ms` }}>
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-burgundy-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-burgundy-200 transition-colors">
                      <service.icon className="w-8 h-8 text-burgundy-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">{service.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Meet Our Expert <span className="text-burgundy-600">Team</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our dedicated professionals bring years of experience and deep market knowledge to serve you better.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  name: "Rajesh Patel",
                  role: "Founder & CEO",
                  experience: "15+ Years Experience",
                  specialization: "Luxury Residential Properties",
                  description: "Leading Ahmedabad's real estate transformation with innovative solutions and client-first approach."
                },
                {
                  name: "Priya Sharma",
                  role: "Senior Property Consultant",
                  experience: "10+ Years Experience",
                  specialization: "Commercial Real Estate",
                  description: "Expert in commercial property investments and business real estate solutions across Gujarat."
                },
                {
                  name: "Amit Desai",
                  role: "Investment Advisor",
                  experience: "8+ Years Experience",
                  specialization: "Property Investment Analysis",
                  description: "Specializes in market analysis and investment strategies for maximum returns on property investments."
                }
              ].map((member, index) => (
                <Card key={member.name} className={`animate-on-scroll opacity-0 translate-y-8 border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white`} style={{ animationDelay: `${index * 200}ms` }}>
                  <CardContent className="p-6 text-center">
                    <div className="w-24 h-24 bg-gradient-to-br from-burgundy-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-12 h-12 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                    <p className="text-burgundy-600 font-semibold mb-2">{member.role}</p>
                    <Badge className="bg-burgundy-100 text-burgundy-800 text-xs mb-3">{member.experience}</Badge>
                    <p className="text-sm font-medium text-gray-700 mb-3">{member.specialization}</p>
                    <p className="text-gray-600 text-sm leading-relaxed">{member.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-12">
              <p className="text-gray-600 mb-6">Want to join our growing team?</p>
              <Button asChild variant="outline" className="border-burgundy-600 text-burgundy-600 hover:bg-burgundy-600 hover:text-white">
                <Link href="/career">View Career Opportunities</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-20 bg-gradient-to-br from-burgundy-50 to-red-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Our Core <span className="text-burgundy-600">Values</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                The principles that guide every decision and interaction in our real estate practice.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: Shield,
                  title: "Transparency",
                  description: "Complete honesty in all dealings with clear communication about processes, costs, and market conditions."
                },
                {
                  icon: Award,
                  title: "Expertise",
                  description: "Deep market knowledge and professional competence to provide the best advice and service quality."
                },
                {
                  icon: Heart,
                  title: "Client-Centric",
                  description: "Putting client needs first with personalized attention and customized solutions for every requirement."
                }
              ].map((value, index) => (
                <div key={value.title} className={`animate-on-scroll opacity-0 translate-y-8 text-center`} style={{ animationDelay: `${index * 200}ms` }}>
                  <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <value.icon className="w-10 h-10 text-burgundy-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Client Testimonials */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                What Our <span className="text-burgundy-600">Clients Say</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Real experiences from satisfied clients who trusted us with their property dreams.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  name: "Kiran Shah",
                  role: "Homeowner",
                  location: "Prahlad Nagar",
                  rating: 5,
                  testimonial: "Dwelling Desire made our home buying journey incredibly smooth. Their transparency and expertise helped us find our dream home in Prahlad Nagar within our budget."
                },
                {
                  name: "Neha Patel",
                  role: "Property Investor",
                  location: "SG Highway",
                  rating: 5,
                  testimonial: "Excellent investment guidance! The team's market analysis helped me make profitable property investments. Their post-sale support is outstanding."
                },
                {
                  name: "Rohit Mehta",
                  role: "NRI Investor",
                  location: "GIFT City",
                  rating: 5,
                  testimonial: "As an NRI, I was concerned about property investment in India. Dwelling Desire's team handled everything professionally and kept me informed throughout the process."
                }
              ].map((testimonial, index) => (
                <Card key={testimonial.name} className={`animate-on-scroll opacity-0 translate-y-8 border-0 shadow-lg bg-white`} style={{ animationDelay: `${index * 200}ms` }}>
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-gray-600 mb-6 leading-relaxed italic">"{testimonial.testimonial}"</p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-burgundy-100 rounded-full flex items-center justify-center mr-4">
                        <Users className="w-6 h-6 text-burgundy-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                        <p className="text-sm text-gray-600">{testimonial.role} • {testimonial.location}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="animate-on-scroll opacity-0 translate-y-8">
                <h2 className="text-4xl font-bold text-gray-900 mb-6">
                  Why Choose <span className="text-burgundy-600">Dwelling Desire?</span>
                </h2>

                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our unique combination of local expertise, innovative technology, and personalized service sets us apart in Ahmedabad's competitive real estate market.
                </p>

                <div className="space-y-6">
                  {[
                    "RERA certified and legally compliant operations",
                    "Extensive network of verified properties and builders",
                    "End-to-end transaction support and documentation",
                    "Market analysis and investment advisory services",
                    "Post-sale support and property management assistance",
                    "Multilingual team fluent in Gujarati, Hindi, and English"
                  ].map((point, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{point}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-burgundy-600 text-white p-6 rounded-2xl text-center">
                    <TrendingUp className="w-8 h-8 mx-auto mb-3" />
                    <div className="text-2xl font-bold mb-2">98%</div>
                    <div className="text-sm opacity-90">Client Satisfaction</div>
                  </div>
                  <div className="bg-gray-900 text-white p-6 rounded-2xl text-center">
                    <Users className="w-8 h-8 mx-auto mb-3" />
                    <div className="text-2xl font-bold mb-2">1000+</div>
                    <div className="text-sm opacity-90">Happy Clients</div>
                  </div>
                  <div className="bg-green-600 text-white p-6 rounded-2xl text-center">
                    <Star className="w-8 h-8 mx-auto mb-3" />
                    <div className="text-2xl font-bold mb-2">4.9/5</div>
                    <div className="text-sm opacity-90">Average Rating</div>
                  </div>
                  <div className="bg-blue-600 text-white p-6 rounded-2xl text-center">
                    <Building2 className="w-8 h-8 mx-auto mb-3" />
                    <div className="text-2xl font-bold mb-2">50+</div>
                    <div className="text-sm opacity-90">Partner Builders</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Start Your Real Estate Journey?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Connect with our expert team today and discover how we can help you achieve your property goals.
              </p>

              <div className="grid md:grid-cols-3 gap-8 mb-12">
                <div className="text-center">
                  <div className="w-16 h-16 bg-burgundy-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Visit Our Office</h3>
                  <p className="text-gray-300 text-sm">
                    123 Business Hub, SG Highway<br />
                    Ahmedabad, Gujarat 380015
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-burgundy-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Call Us</h3>
                  <p className="text-gray-300 text-sm">
                    +91 98765 43210<br />
                    +91 87654 32109
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-burgundy-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Mail className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Email Us</h3>
                  <p className="text-gray-300 text-sm">
                    <EMAIL><br />
                    <EMAIL>
                  </p>
                </div>
              </div>

              <Button asChild size="lg" className="bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 py-3 rounded-full font-semibold">
                <Link href="/contact">Get In Touch</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

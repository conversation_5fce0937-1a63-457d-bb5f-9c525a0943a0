"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Building2,
  Users,
  Award,
  Target,
  Eye,
  Heart,
  Shield,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  CheckCircle,
  Star,
  Home,
  Key,
  Calculator,
  Handshake,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function AboutContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-[60vh] flex items-center justify-center bg-gradient-to-br from-burgundy-900 via-burgundy-800 to-red-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/images/pattern.svg')] bg-repeat opacity-20"></div>
        </div>

        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/hero-bg.jpg"
            alt="About Dwelling Desire"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <BookOpen size={16} className="mr-2" />
              About Us
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              About <span className="text-yellow-300">Dwelling Desire</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Your trusted partner in Ahmedabad's real estate journey with expertise, transparency, and commitment to excellence.
            </p>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Welcome to <span className="text-burgundy-600">Dwelling Desire</span>
              </h2>

              <div className="prose prose-lg max-w-none text-gray-600 leading-relaxed space-y-6">
                <p>
                  Founded in 2019, Dwelling Desire has emerged as Ahmedabad's premier real estate consultancy, specializing in residential and commercial properties across Gujarat's most sought-after locations.
                </p>

                <p>
                  With deep local market knowledge and a commitment to transparent dealings, we've successfully facilitated over 500+ property transactions, helping families find their dream homes and investors discover lucrative opportunities.
                </p>

                <h3 className="text-2xl font-bold text-gray-900 mt-8 mb-4">Our Mission</h3>
                <p>
                  To provide exceptional real estate services that exceed client expectations through transparent dealings, expert market knowledge, and personalized attention. We strive to make property transactions seamless, secure, and rewarding for every client.
                </p>

                <h3 className="text-2xl font-bold text-gray-900 mt-8 mb-4">Our Vision</h3>
                <p>
                  To become Gujarat's most trusted and innovative real estate consultancy, setting new standards in customer service and market expertise. We envision a future where every property transaction is a stepping stone to our clients' success and happiness.
                </p>

                <h3 className="text-2xl font-bold text-gray-900 mt-8 mb-4">Our Services</h3>
                <ul className="list-disc list-inside space-y-2">
                  <li><strong>Property Buying:</strong> Expert guidance in finding and purchasing your dream property</li>
                  <li><strong>Property Selling:</strong> Strategic marketing to achieve the best value for your investment</li>
                  <li><strong>Leasing Services:</strong> Comprehensive rental solutions for landlords and tenants</li>
                  <li><strong>Investment Consultation:</strong> Data-driven advice to maximize returns</li>
                  <li><strong>Property Management:</strong> End-to-end property management services</li>
                </ul>

                <h3 className="text-2xl font-bold text-gray-900 mt-8 mb-4">Why Choose Dwelling Desire?</h3>
                <ul className="list-disc list-inside space-y-2">
                  <li>RERA certified and legally compliant operations</li>
                  <li>Extensive network of verified properties and builders</li>
                  <li>End-to-end transaction support and documentation</li>
                  <li>Market analysis and investment advisory services</li>
                  <li>Post-sale support and property management assistance</li>
                  <li>Multilingual team fluent in Gujarati, Hindi, and English</li>
                </ul>

                <div className="bg-burgundy-50 p-6 rounded-xl mt-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Get in Touch</h3>
                  <p className="mb-4">
                    Ready to start your real estate journey? Our expert team is here to help you achieve your property goals.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button asChild className="bg-burgundy-600 hover:bg-burgundy-700">
                      <Link href="/contact">Contact Us</Link>
                    </Button>
                    <Button asChild variant="outline" className="border-burgundy-600 text-burgundy-600 hover:bg-burgundy-600 hover:text-white">
                      <Link href="/properties">View Properties</Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


    </main>
  )
}

"use client"

import type React from "react"
import { useEffect, useRef, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Send, 
  MessageCircle, 
  ArrowLeft, 
  Building
} from "lucide-react"
import Link from "next/link"

export default function ContactPageContent() {
  const sectionRef = useRef<HTMLElement>(null)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  })

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Form submitted:", formData)
  }

  return (
    <main className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <MessageCircle size={16} className="mr-2" />
              Get in Touch
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Contact <span className="text-yellow-300">Dwelling Desire</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Ready to find your dream property? Let's start the conversation.
            </p>
          </div>
        </div>
      </section>

      {/* Main Contact Section */}
      <section ref={sectionRef} className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-5 gap-12">
            {/* Contact Information */}
            <div className="lg:col-span-2 animate-on-scroll opacity-0 translate-x-8">
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Contact <span className="text-burgundy-600">Information</span></h2>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    We're here to help you with all your real estate needs. Get in touch with us today.
                  </p>
                </div>

                {/* Contact Cards */}
                <div className="space-y-6">
                  <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-xl">
                    <div className="bg-burgundy-100 p-3 rounded-xl">
                      <MapPin className="text-burgundy-600" size={24} />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 mb-2">Office Address</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Titanium Heights, A-705,<br />
                        opp. Vodafone House,<br />
                        Prahlad Nagar, Ahmedabad,<br />
                        Gujarat 380015
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-xl">
                    <div className="bg-green-100 p-3 rounded-xl">
                      <Phone className="text-green-600" size={24} />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 mb-2">Phone Numbers</h3>
                      <p className="text-gray-600">
                        <a href="tel:+918141112929" className="hover:text-burgundy-600">+91 81411 12929</a><br/>
                        <a href="tel:+919898631401" className="hover:text-burgundy-600">+91 98986 31401</a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-xl">
                    <div className="bg-blue-100 p-3 rounded-xl">
                      <Mail className="text-blue-600" size={24} />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 mb-2">Email Address</h3>
                      <p className="text-gray-600">
                        <a href="mailto:<EMAIL>" className="hover:text-burgundy-600"><EMAIL></a><br />
                        <a href="mailto:<EMAIL>" className="hover:text-burgundy-600"><EMAIL></a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 p-6 bg-gray-50 rounded-xl">
                    <div className="bg-orange-100 p-3 rounded-xl">
                      <Clock className="text-orange-600" size={24} />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 mb-2">Business Hours</h3>
                      <p className="text-gray-600">
                        Mon - Sat: 9:00 AM - 7:00 PM<br />
                        Sunday: 10:00 AM - 5:00 PM
                      </p>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-burgundy-50 p-6 rounded-xl">
                  <h3 className="font-bold text-gray-900 mb-4">Quick Contact</h3>
                  <div className="flex gap-3">
                    <Button 
                      asChild
                      className="bg-burgundy-600 hover:bg-burgundy-700 text-white"
                    >
                      <a href="tel:+918141112929">
                        <Phone size={16} className="mr-2" />
                        Call Now
                      </a>
                    </Button>
                    <Button 
                      asChild
                      variant="outline" 
                      className="border-burgundy-200 text-burgundy-600 hover:bg-burgundy-50"
                    >
                      <a href="https://wa.me/918141112929" target="_blank" rel="noopener noreferrer">
                        <MessageCircle size={16} className="mr-2" />
                        WhatsApp
                      </a>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-3 animate-on-scroll opacity-0 translate-x-8 delay-200">
              <Card className="border-0 shadow-xl bg-white">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <h2 className="text-3xl font-bold text-gray-900 mb-3">Send us a <span className="text-burgundy-600">Message</span></h2>
                    <p className="text-gray-600 text-lg">We'll get back to you within 24 hours.</p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Full Name</label>
                        <Input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          placeholder="Enter your full name"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Email Address</label>
                        <Input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder="Enter your email"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Phone Number</label>
                        <Input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="Enter your phone number"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Subject</label>
                        <Input
                          type="text"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          placeholder="What's this about?"
                          className="h-12 rounded-xl border-2 border-burgundy-500 focus:border-burgundy-600 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Message</label>
                      <Textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="Tell us about your requirements..."
                        rows={5}
                        className="rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0 resize-none"
                        required
                      />
                    </div>

                    <Button
                      type="submit"
                      size="lg"
                      className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group"
                    >
                      Send Message
                      <Send className="ml-2 group-hover:translate-x-1 transition-transform" size={18} />
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-700 mb-4">
              <Building size={16} className="mr-2" />
              Find Our Office
            </Badge>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Visit Our <span className="text-burgundy-600">Office</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Located in the heart of Prahlad Nagar, easily accessible with ample parking.
            </p>
          </div>
          
          <Card className="border-0 shadow-xl overflow-hidden max-w-5xl mx-auto">
            <CardContent className="p-0 relative">
              <div className="relative bg-gray-100 h-96">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3672.632166011687!2d72.50231910000001!3d23.000549900000003!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e9ba5a59f7421%3A0x48dc1bc0318363f!2sDWELLING%20DESIRE!5e0!3m2!1sen!2sin!4v1752388218786!5m2!1sen!2sin"
                  width="100%"
                  height="384"
                  style={{ border: 0 }}
                  allowFullScreen={true}
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Dwelling Desire Office Location"
                  className="w-full h-96"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </main>
  )
} 

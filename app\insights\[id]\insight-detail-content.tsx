"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  User, 
  Calendar, 
  Clock, 
  ArrowLeft, 
  Share2,
  BookOpen,
  Tag,
  ArrowRight
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

type InsightProps = {
  insight: {
    id: number
    title: string
    excerpt: string
    image: string
    category: string
    author: string
    date: string
    readTime: string
    featured: boolean
    tags: string[]
    content: string
  }
}

// Related articles (simplified for demo)
const relatedInsights = [
  {
    id: 2,
    title: "Top 10 Localities for Property Investment",
    excerpt: "Explore the most promising areas in Ahmedabad for real estate investment.",
    image: "/images/insights/2.webp",
    category: "Investment",
    readTime: "7 min"
  },
  {
    id: 3,
    title: "Home Buying Guide for First-Time Buyers",
    excerpt: "A comprehensive guide for first-time home buyers.",
    image: "/images/insights/3.webp",
    category: "Guide",
    readTime: "6 min"
  }
]

export default function InsightDetailContent({ insight }: InsightProps) {
  const sectionRef = useRef<HTMLElement>(null)

  // Animation observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: insight.title,
        text: insight.excerpt,
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Link copied to clipboard!")
    }
  }

  return (
    <main ref={sectionRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-12 lg:pt-28 lg:pb-16 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <div className="mb-6">
              <Button
                variant="ghost"
                className="text-white hover:bg-white/20 p-2 rounded-full"
                asChild
              >
                <Link href="/insights">
                  <ArrowLeft size={20} className="mr-2" />
                  Back to Insights
                </Link>
              </Button>
            </div>

            {/* Article Meta */}
            <div className="mb-6">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-4 px-4 py-2 text-sm">
                <BookOpen size={16} className="mr-2" />
                {insight.category}
              </Badge>

              <h1 className="text-3xl lg:text-4xl font-bold mb-6 leading-tight">
                {insight.title}
              </h1>

              <div className="flex flex-wrap items-center gap-6 text-sm text-white/90">
                <div className="flex items-center space-x-2">
                  <User size={16} />
                  <span>{insight.author}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar size={16} />
                  <span suppressHydrationWarning>
                    {new Date(insight.date).toLocaleDateString('en-IN')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock size={16} />
                  <span>{insight.readTime}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="text-white hover:bg-white/20 p-2 rounded-full"
                >
                  <Share2 size={16} />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-4 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-3">
                {/* Featured Image */}
                <div className="animate-on-scroll opacity-0 translate-y-8 mb-8">
                  <Image
                    src={insight.image || "/placeholder.svg"}
                    alt={insight.title}
                    width={800}
                    height={400}
                    className="w-full h-64 lg:h-96 object-cover rounded-xl shadow-lg"
                  />
                </div>

                {/* Article Content */}
                <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
                  <div
                    className="blog-content prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-bold prose-p:text-gray-600 prose-p:leading-relaxed prose-a:text-burgundy-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 prose-ul:text-gray-600 prose-li:text-gray-600"
                    dangerouslySetInnerHTML={{ __html: insight.content }}
                  />

                  <style jsx>{`
                    .blog-content {
                      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                      line-height: 1.7;
                      color: #374151;
                    }

                    .blog-content .lead-paragraph {
                      font-size: 1.25rem;
                      line-height: 1.8;
                      color: #4B5563;
                      font-weight: 500;
                      margin-bottom: 2.5rem;
                      padding: 2rem;
                      background: linear-gradient(135deg, #FEF7F0 0%, #FDF2F8 100%);
                      border-radius: 16px;
                      border-left: 5px solid #7C2D12;
                      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    }

                    .blog-content h2 {
                      font-size: 2rem;
                      font-weight: 700;
                      margin-top: 3.5rem;
                      margin-bottom: 1.5rem;
                      color: #1F2937;
                      border-bottom: 3px solid #7C2D12;
                      padding-bottom: 0.75rem;
                      position: relative;
                    }

                    .blog-content h2::before {
                      content: '';
                      position: absolute;
                      bottom: -3px;
                      left: 0;
                      width: 60px;
                      height: 3px;
                      background: #DC2626;
                    }

                    .blog-content h3 {
                      font-size: 1.5rem;
                      font-weight: 600;
                      margin-top: 2.5rem;
                      margin-bottom: 1.25rem;
                      color: #374151;
                      position: relative;
                      padding-left: 1rem;
                    }

                    .blog-content h3::before {
                      content: '';
                      position: absolute;
                      left: 0;
                      top: 50%;
                      transform: translateY(-50%);
                      width: 4px;
                      height: 24px;
                      background: #7C2D12;
                      border-radius: 2px;
                    }

                    .blog-content h4 {
                      font-size: 1.25rem;
                      font-weight: 600;
                      margin-top: 2rem;
                      margin-bottom: 1rem;
                      color: #374151;
                    }

                    .blog-content p {
                      margin-bottom: 1.5rem;
                      line-height: 1.8;
                      color: #4B5563;
                      font-size: 1.1rem;
                    }

                    .blog-content strong {
                      font-weight: 600;
                      color: #1F2937;
                    }

                    .blog-content ul {
                      margin: 1.5rem 0;
                      padding-left: 0;
                      list-style: none;
                    }

                    .blog-content ul li {
                      margin: 0.75rem 0;
                      padding-left: 2rem;
                      line-height: 1.7;
                      position: relative;
                      color: #4B5563;
                    }

                    .blog-content ul li::before {
                      content: '▶';
                      position: absolute;
                      left: 0.5rem;
                      color: #7C2D12;
                      font-size: 0.8rem;
                      top: 0.1rem;
                    }

                    .blog-content ol {
                      margin: 1.5rem 0;
                      padding-left: 2rem;
                      counter-reset: item;
                    }

                    .blog-content ol li {
                      margin: 0.75rem 0;
                      line-height: 1.7;
                      color: #4B5563;
                      counter-increment: item;
                      position: relative;
                    }

                    .blog-content ol li::before {
                      content: counter(item);
                      position: absolute;
                      left: -2rem;
                      top: 0;
                      background: #7C2D12;
                      color: white;
                      width: 1.5rem;
                      height: 1.5rem;
                      border-radius: 50%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 0.8rem;
                      font-weight: 600;
                    }

                    .blog-content .locality-card,
                    .blog-content .expert-tip,
                    .blog-content .investment-tips,
                    .blog-content .cta-section,
                    .blog-content .budget-calculator,
                    .blog-content .documents-grid,
                    .blog-content .location-factors,
                    .blog-content .areas-recommendation,
                    .blog-content .property-checklist,
                    .blog-content .legal-warning,
                    .blog-content .success-tips {
                      margin: 2.5rem 0;
                      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    }

                    .blog-content table {
                      margin: 2.5rem 0;
                      box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
                      border-radius: 12px;
                      overflow: hidden;
                      width: 100%;
                      border-collapse: collapse;
                    }

                    .blog-content table th {
                      background: linear-gradient(135deg, #7C2D12 0%, #991B1B 100%);
                      color: white;
                      font-weight: 600;
                      padding: 1rem;
                      text-align: left;
                      font-size: 0.95rem;
                    }

                    .blog-content table td {
                      padding: 1rem;
                      border-bottom: 1px solid #E5E7EB;
                      font-size: 0.95rem;
                    }

                    .blog-content table tr:hover {
                      background-color: #F9FAFB;
                    }

                    .blog-content .comparison-grid {
                      margin: 2.5rem 0;
                    }

                    .blog-content blockquote {
                      margin: 2rem 0;
                      padding: 1.5rem 2rem;
                      background: #F0F9FF;
                      border-left: 4px solid #0EA5E9;
                      border-radius: 8px;
                      font-style: italic;
                      color: #0F172A;
                    }

                    .blog-content .highlight-box {
                      background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
                      border: 1px solid #F59E0B;
                      border-radius: 12px;
                      padding: 1.5rem;
                      margin: 2rem 0;
                    }

                    .blog-content .warning-box {
                      background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
                      border: 1px solid #EF4444;
                      border-radius: 12px;
                      padding: 1.5rem;
                      margin: 2rem 0;
                    }

                    .blog-content .success-box {
                      background: linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%);
                      border: 1px solid #10B981;
                      border-radius: 12px;
                      padding: 1.5rem;
                      margin: 2rem 0;
                    }

                    .blog-content .info-box {
                      background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
                      border: 1px solid #3B82F6;
                      border-radius: 12px;
                      padding: 1.5rem;
                      margin: 2rem 0;
                    }
                  `}</style>
                </div>

                {/* Tags */}
                <div className="animate-on-scroll opacity-0 translate-y-8 delay-400 mt-12 pt-8 border-t border-gray-200">
                  <div className="flex items-center space-x-2 mb-4">
                    <Tag size={16} className="text-gray-500" />
                    <span className="text-sm font-semibold text-gray-700">Tags:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {insight.tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="outline"
                        className="text-xs border-gray-200 text-gray-600 hover:bg-gray-50"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar - Empty for now, content moved to bottom */}
              <div className="lg:col-span-1">
                <div className="sticky top-24 space-y-8">
                  {/* Sidebar content can be added here if needed */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="py-16 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="w-20 h-20 bg-gradient-to-br from-burgundy-500 to-red-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>

              <h2 className="text-4xl font-bold text-white mb-4">
                Stay <span className="text-burgundy-400">Updated</span>
              </h2>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Get the latest real estate insights, market trends, and property tips delivered straight to your inbox.
              </p>

              <div className="max-w-md mx-auto">
                <div className="flex flex-col sm:flex-row gap-4">
                  <input
                    type="email"
                    placeholder="Enter your email address"
                    className="flex-1 px-6 py-4 rounded-2xl border border-gray-600 bg-gray-800/50 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-burgundy-500 focus:border-transparent backdrop-blur-sm"
                  />
                  <Button className="bg-gradient-to-r from-burgundy-500 to-red-600 hover:from-burgundy-600 hover:to-red-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-xl whitespace-nowrap">
                    Subscribe Now
                  </Button>
                </div>

                <p className="text-sm text-gray-400 mt-4">
                  📧 Weekly insights • 🚫 No spam • ✅ Unsubscribe anytime
                </p>
              </div>

              <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="text-gray-300">
                  <div className="text-2xl font-bold text-burgundy-400 mb-2">5000+</div>
                  <div className="text-sm">Subscribers</div>
                </div>
                <div className="text-gray-300">
                  <div className="text-2xl font-bold text-burgundy-400 mb-2">Weekly</div>
                  <div className="text-sm">Market Updates</div>
                </div>
                <div className="text-gray-300">
                  <div className="text-2xl font-bold text-burgundy-400 mb-2">Expert</div>
                  <div className="text-sm">Insights</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Related <span className="text-burgundy-600">Articles</span>
              </h2>
              <p className="text-gray-600">Continue exploring our insights</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {relatedInsights.filter(related => related.id !== insight.id).slice(0, 2).map((article, index) => (
                <Card
                  key={article.id}
                  className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-lg transition-all duration-300 border-0 bg-white overflow-hidden`}
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className="relative overflow-hidden">
                    <Image
                      src={article.image || "/placeholder.svg"}
                      alt={article.title}
                      width={400}
                      height={200}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-white/90 text-gray-700 hover:bg-white/90 text-xs font-medium">
                        {article.category}
                      </Badge>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-burgundy-600 transition-colors">
                      {article.title}
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {article.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{article.readTime}</span>
                      <Button
                        variant="ghost"
                        className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 p-0 h-auto font-semibold group/btn"
                        asChild
                      >
                        <Link href={`/insights/${article.id}`}>
                          Read More
                          <ArrowRight className="ml-2 group-hover/btn:translate-x-1 transition-transform" size={14} />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-12">
              <Button
                variant="outline"
                className="border-2 border-burgundy-600 text-burgundy-600 hover:bg-burgundy-600 hover:text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 group"
                asChild
              >
                <Link href="/insights">
                  View All Insights
                  <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={16} />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

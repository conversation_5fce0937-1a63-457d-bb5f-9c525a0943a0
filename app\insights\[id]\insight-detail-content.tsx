"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  User, 
  Calendar, 
  Clock, 
  ArrowLeft, 
  Share2,
  BookOpen,
  Tag,
  ArrowRight
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

type InsightProps = {
  insight: {
    id: number
    title: string
    excerpt: string
    image: string
    category: string
    author: string
    date: string
    readTime: string
    featured: boolean
    tags: string[]
    content: string
  }
}

// Related articles (simplified for demo)
const relatedInsights = [
  {
    id: 2,
    title: "Top 10 Localities for Property Investment",
    excerpt: "Explore the most promising areas in Ahmedabad for real estate investment.",
    image: "/images/insights/2.webp",
    category: "Investment",
    readTime: "7 min"
  },
  {
    id: 3,
    title: "Home Buying Guide for First-Time Buyers",
    excerpt: "A comprehensive guide for first-time home buyers.",
    image: "/images/insights/3.webp",
    category: "Guide",
    readTime: "6 min"
  }
]

export default function InsightDetailContent({ insight }: InsightProps) {
  const sectionRef = useRef<HTMLElement>(null)

  // Animation observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: insight.title,
        text: insight.excerpt,
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Link copied to clipboard!")
    }
  }

  return (
    <main ref={sectionRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-12 lg:pt-28 lg:pb-16 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <div className="mb-6">
              <Button
                variant="ghost"
                className="text-white hover:bg-white/20 p-2 rounded-full"
                asChild
              >
                <Link href="/insights">
                  <ArrowLeft size={20} className="mr-2" />
                  Back to Insights
                </Link>
              </Button>
            </div>

            {/* Article Meta */}
            <div className="mb-6">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-4 px-4 py-2 text-sm">
                <BookOpen size={16} className="mr-2" />
                {insight.category}
              </Badge>

              <h1 className="text-3xl lg:text-4xl font-bold mb-6 leading-tight">
                {insight.title}
              </h1>

              <div className="flex flex-wrap items-center gap-6 text-sm text-white/90">
                <div className="flex items-center space-x-2">
                  <User size={16} />
                  <span>{insight.author}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar size={16} />
                  <span suppressHydrationWarning>
                    {new Date(insight.date).toLocaleDateString('en-IN')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock size={16} />
                  <span>{insight.readTime}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="text-white hover:bg-white/20 p-2 rounded-full"
                >
                  <Share2 size={16} />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-4 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-3">
                {/* Featured Image */}
                <div className="animate-on-scroll opacity-0 translate-y-8 mb-8">
                  <Image
                    src={insight.image || "/placeholder.svg"}
                    alt={insight.title}
                    width={800}
                    height={400}
                    className="w-full h-64 lg:h-96 object-cover rounded-xl shadow-lg"
                  />
                </div>

                {/* Article Content */}
                <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
                  <div
                    className="blog-content prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-bold prose-p:text-gray-600 prose-p:leading-relaxed prose-a:text-burgundy-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 prose-ul:text-gray-600 prose-li:text-gray-600"
                    dangerouslySetInnerHTML={{ __html: insight.content }}
                  />

                  <style jsx>{`
                    .blog-content .lead-paragraph {
                      font-size: 1.25rem;
                      line-height: 1.8;
                      color: #4B5563;
                      font-weight: 500;
                      margin-bottom: 2rem;
                      padding: 1.5rem;
                      background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
                      border-radius: 12px;
                      border-left: 4px solid #7C2D12;
                    }

                    .blog-content h2 {
                      font-size: 1.875rem;
                      margin-top: 3rem;
                      margin-bottom: 1.5rem;
                      color: #1F2937;
                      border-bottom: 2px solid #E5E7EB;
                      padding-bottom: 0.5rem;
                    }

                    .blog-content h3 {
                      font-size: 1.5rem;
                      margin-top: 2rem;
                      margin-bottom: 1rem;
                      color: #374151;
                    }

                    .blog-content .locality-card,
                    .blog-content .expert-tip,
                    .blog-content .investment-tips,
                    .blog-content .cta-section,
                    .blog-content .budget-calculator,
                    .blog-content .documents-grid,
                    .blog-content .location-factors,
                    .blog-content .areas-recommendation,
                    .blog-content .property-checklist,
                    .blog-content .legal-warning,
                    .blog-content .success-tips {
                      margin: 2rem 0;
                    }

                    .blog-content table {
                      margin: 2rem 0;
                      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                      border-radius: 8px;
                      overflow: hidden;
                    }

                    .blog-content .comparison-grid {
                      margin: 2rem 0;
                    }

                    .blog-content ul {
                      margin: 1rem 0;
                      padding-left: 1.5rem;
                    }

                    .blog-content li {
                      margin: 0.5rem 0;
                      line-height: 1.6;
                    }

                    .blog-content .price-table table th {
                      background-color: #FEF2F2;
                      color: #7C2D12;
                      font-weight: 600;
                    }

                    .blog-content .loan-rates table th {
                      background-color: #FEF2F2;
                      color: #7C2D12;
                      font-weight: 600;
                    }
                  `}</style>
                </div>

                {/* Tags */}
                <div className="animate-on-scroll opacity-0 translate-y-8 delay-400 mt-12 pt-8 border-t border-gray-200">
                  <div className="flex items-center space-x-2 mb-4">
                    <Tag size={16} className="text-gray-500" />
                    <span className="text-sm font-semibold text-gray-700">Tags:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {insight.tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="outline"
                        className="text-xs border-gray-200 text-gray-600 hover:bg-gray-50"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-24 space-y-8">
                  {/* Author Info */}
                  <Card className="animate-on-scroll opacity-0 translate-y-8 delay-300 border-0 shadow-lg">
                    <CardContent className="p-6">
                      <h4 className="font-semibold text-gray-900 mb-3">About the Author</h4>
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-12 h-12 bg-burgundy-100 rounded-full flex items-center justify-center">
                          <User size={20} className="text-burgundy-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{insight.author}</p>
                          <p className="text-sm text-gray-600">Real Estate Expert</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600">
                        Experienced real estate professional with deep knowledge of Ahmedabad's property market.
                      </p>
                    </CardContent>
                  </Card>

                  {/* Newsletter Signup */}
                  <Card className="animate-on-scroll opacity-0 translate-y-8 delay-500 border-0 shadow-lg bg-gradient-to-br from-burgundy-50 to-burgundy-100">
                    <CardContent className="p-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Stay Updated</h4>
                      <p className="text-sm text-gray-600 mb-4">
                        Get the latest insights delivered to your inbox.
                      </p>
                      <Button className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white rounded-xl">
                        Subscribe Now
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Related <span className="text-burgundy-600">Articles</span>
              </h2>
              <p className="text-gray-600">Continue exploring our insights</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {relatedInsights.filter(related => related.id !== insight.id).slice(0, 2).map((article, index) => (
                <Card
                  key={article.id}
                  className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-lg transition-all duration-300 border-0 bg-white overflow-hidden`}
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className="relative overflow-hidden">
                    <Image
                      src={article.image || "/placeholder.svg"}
                      alt={article.title}
                      width={400}
                      height={200}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-white/90 text-gray-700 hover:bg-white/90 text-xs font-medium">
                        {article.category}
                      </Badge>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-burgundy-600 transition-colors">
                      {article.title}
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {article.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{article.readTime}</span>
                      <Button
                        variant="ghost"
                        className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 p-0 h-auto font-semibold group/btn"
                        asChild
                      >
                        <Link href={`/insights/${article.id}`}>
                          Read More
                          <ArrowRight className="ml-2 group-hover/btn:translate-x-1 transition-transform" size={14} />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-12">
              <Button
                variant="outline"
                className="border-2 border-burgundy-600 text-burgundy-600 hover:bg-burgundy-600 hover:text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 group"
                asChild
              >
                <Link href="/insights">
                  View All Insights
                  <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={16} />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

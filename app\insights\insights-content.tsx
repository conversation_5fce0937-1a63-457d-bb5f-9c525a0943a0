"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  BookOpen, 
  User, 
  Calendar, 
  Clock, 
  ArrowRight, 
  Search,
  Filter,
  TrendingUp,
  Target,
  ChevronDown
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Blog data - same as used in home page
const insights = [
  {
    id: 1,
    title: "Real Estate Market Trends in Ahmedabad 2025",
    excerpt: "Discover the latest market trends, price movements, and investment opportunities in Ahmedabad's real estate sector.",
    image: "/images/insights/1.webp",
    category: "Market Analysis",
    author: "Dwelling Desire Team",
    date: "2024-07-15",
    readTime: "5 min read",
    featured: true,
    tags: ["market trends", "ahmedabad", "investment", "2025"],
    content: "Detailed analysis of Ahmedabad's real estate market trends for 2025..."
  },
  {
    id: 2,
    title: "Top 10 Localities for Property Investment",
    excerpt: "Explore the most promising areas in Ahmedabad for real estate investment with high growth potential.",
    image: "/images/insights/2.webp",
    category: "Investment",
    author: "Rajesh <PERSON>",
    date: "2024-06-10",
    readTime: "7 min",
    featured: false,
    tags: ["investment", "localities", "growth", "ahmedabad"],
    content: "Comprehensive guide to the best investment localities in Ahmedabad..."
  },
  {
    id: 3,
    title: "Home Buying Guide for First-Time Buyers",
    excerpt: "A comprehensive guide covering everything first-time home buyers need to know about purchasing property.",
    image: "/images/insights/3.webp",
    category: "Guide",
    author: "Priya Sharma",
    date: "2024-06-05",
    readTime: "6 min",
    featured: false,
    tags: ["home buying", "first time", "guide", "tips"],
    content: "Step-by-step guide for first-time home buyers..."
  },
  {
    id: 4,
    title: "Commercial Real Estate Opportunities",
    excerpt: "Discover lucrative commercial real estate opportunities in Ahmedabad's growing business districts.",
    image: "/images/insights/4.webp",
    category: "Commercial",
    author: "Amit Desai",
    date: "2024-05-01",
    readTime: "8 min",
    featured: false,
    tags: ["commercial", "business", "opportunities", "investment"],
    content: "Analysis of commercial real estate opportunities..."
  },
  {
    id: 5,
    title: "Understanding Property Valuation Methods",
    excerpt: "Learn about different property valuation methods and how they affect your buying and selling decisions.",
    image: "/images/insights/5.webp",
    category: "Guide",
    author: "Neha Patel",
    date: "2024-04-20",
    readTime: "5 min",
    featured: false,
    tags: ["valuation", "property", "methods", "guide"],
    content: "Comprehensive guide to property valuation methods..."
  },
  {
    id: 6,
    title: "Real Estate Investment vs. Other Investment Options",
    excerpt: "Compare real estate investment with stocks, bonds, and other investment vehicles to make informed decisions.",
    image: "/images/insights/6.webp",
    category: "Investment",
    author: "Kiran Shah",
    date: "2024-04-15",
    readTime: "9 min",
    featured: false,
    tags: ["investment", "comparison", "real estate", "stocks"],
    content: "Detailed comparison of investment options..."
  }
]

const categories = ["All", "Market Analysis", "Investment", "Guide", "Commercial"]

export default function InsightsPageContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [filteredInsights, setFilteredInsights] = useState(insights)
  const [showFilters, setShowFilters] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)

  // Filter insights based on search and category
  useEffect(() => {
    let filtered = insights

    if (selectedCategory !== "All") {
      filtered = filtered.filter(insight => insight.category === selectedCategory)
    }

    if (searchTerm) {
      filtered = filtered.filter(insight => 
        insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    setFilteredInsights(filtered)
  }, [searchTerm, selectedCategory])

  // Animation observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [filteredInsights])

  const featuredInsight = insights.find(insight => insight.featured)

  return (
    <main ref={sectionRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <BookOpen size={16} className="mr-2" />
              Expert Insights
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Real Estate <span className="text-yellow-300">Insights</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Stay informed with expert insights, market trends, and comprehensive guides 
              to make smart real estate decisions.
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-12 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              {/* Search Bar */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <Input
                  type="text"
                  placeholder="Search insights, guides, and market trends..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                />
              </div>
              
              {/* Filter Toggle */}
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="h-12 px-6 rounded-xl border border-gray-200 hover:bg-gray-50"
              >
                <Filter size={16} className="mr-2" />
                Filters
                <ChevronDown size={16} className={`ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
              </Button>
            </div>

            {/* Category Filters */}
            {showFilters && (
              <div className="animate-on-scroll opacity-0 translate-y-4 bg-gray-50 p-6 rounded-xl">
                <h4 className="font-semibold text-gray-900 mb-4">Categories</h4>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className={`rounded-full ${
                        selectedCategory === category 
                          ? 'bg-burgundy-600 hover:bg-burgundy-700 text-white' 
                          : 'border-gray-200 text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Featured Article Section */}
      {featuredInsight && (
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <TrendingUp size={16} className="mr-2" />
                  Featured Article
                </Badge>
                <h2 className="text-3xl font-bold text-gray-900">
                  Editor's <span className="text-burgundy-600">Pick</span>
                </h2>
              </div>

              <Card className="animate-on-scroll opacity-0 translate-y-8 overflow-hidden border-0 shadow-xl bg-white">
                <div className="grid lg:grid-cols-5 gap-0">
                  <div className="lg:col-span-3 p-8 lg:p-12">
                    <Badge className="bg-burgundy-100 text-burgundy-700 hover:bg-burgundy-100 mb-6 px-4 py-2">
                      {featuredInsight.category}
                    </Badge>
                    <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                      {featuredInsight.title}
                    </h3>
                    <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                      {featuredInsight.excerpt}
                    </p>

                    <div className="flex items-center space-x-6 mb-8 text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <User size={16} />
                        <span>{featuredInsight.author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar size={16} />
                        <span suppressHydrationWarning>
                          {new Date(featuredInsight.date).toLocaleDateString('en-IN')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock size={16} />
                        <span>{featuredInsight.readTime}</span>
                      </div>
                    </div>

                    <Button
                      className="bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 py-3 rounded-full font-semibold group"
                      asChild
                    >
                      <Link href={`/insights/${featuredInsight.id}`}>
                        Read Full Article
                        <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={16} />
                      </Link>
                    </Button>
                  </div>

                  <div className="lg:col-span-2 relative">
                    <Image
                      src={featuredInsight.image || "/placeholder.svg"}
                      alt={featuredInsight.title}
                      width={600}
                      height={400}
                      className="w-full h-64 lg:h-full object-cover"
                    />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </section>
      )}

      {/* All Articles Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-12">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  All <span className="text-burgundy-600">Insights</span>
                </h2>
                <p className="text-gray-600">
                  {filteredInsights.length} article{filteredInsights.length !== 1 ? 's' : ''} found
                  {selectedCategory !== "All" && ` in ${selectedCategory}`}
                  {searchTerm && ` for "${searchTerm}"`}
                </p>
              </div>
            </div>

            {filteredInsights.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredInsights.map((article, index) => (
                  <Card
                    key={article.id}
                    className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-lg transition-all duration-300 border-0 bg-white overflow-hidden hover:-translate-y-1`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="relative overflow-hidden">
                      <Image
                        src={article.image || "/placeholder.svg"}
                        alt={article.title}
                        width={400}
                        height={250}
                        className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-white/90 text-gray-700 hover:bg-white/90 text-xs font-medium">
                          {article.category}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-burgundy-600 transition-colors">
                        {article.title}
                      </h3>

                      <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3">
                        {article.excerpt}
                      </p>

                      <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                        <div className="flex items-center space-x-2">
                          <User size={14} />
                          <span>{article.author}</span>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span suppressHydrationWarning>
                            {new Date(article.date).toLocaleDateString('en-IN')}
                          </span>
                          <span>•</span>
                          <span>{article.readTime}</span>
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 p-0 h-auto font-semibold group/btn w-full justify-start"
                        asChild
                      >
                        <Link href={`/insights/${article.id}`}>
                          Read More
                          <ArrowRight className="ml-2 group-hover/btn:translate-x-1 transition-transform" size={14} />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setSelectedCategory("All")
                  }}
                  className="border-burgundy-200 text-burgundy-600 hover:bg-burgundy-50"
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Card className="animate-on-scroll opacity-0 translate-y-8 border-0 bg-white shadow-lg overflow-hidden">
              <CardContent className="p-8 lg:p-12 text-center">
                <div className="mb-6">
                  <Target className="w-12 h-12 text-burgundy-600 mx-auto mb-4" />
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    Stay Updated with Market Insights
                  </h3>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Get the latest real estate insights, market trends, and investment opportunities
                    delivered directly to your inbox.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                  />
                  <Button className="h-12 bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 rounded-xl font-semibold">
                    Subscribe
                  </Button>
                </div>

                <p className="text-sm text-gray-500 mt-4">
                  No spam, unsubscribe at any time.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  )
}

"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  Users, 
  TrendingUp, 
  Award, 
  Heart,
  MapPin,
  Clock,
  DollarSign,
  Briefcase,
  GraduationCap,
  Star,
  Upload,
  Send,
  Building2,
  Target,
  Zap,
  Shield,
  Coffee,
  Car,
  Plane,
  Gift
} from "lucide-react"

export default function CareerPageContent() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    experience: "",
    coverLetter: "",
    resume: null as File | null
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Set initial hidden state after component mounts
    const elements = document.querySelectorAll('.animate-on-scroll')
    elements.forEach(el => {
      el.classList.add('opacity-0', 'translate-y-8')
    })
    setIsLoaded(true)

    const animateElements = () => {
      elements.forEach((el, index) => {
        setTimeout(() => {
          el.classList.add('opacity-100', 'translate-y-0', 'translate-x-0')
          el.classList.remove('opacity-0', 'translate-y-8', 'translate-x-8')
        }, index * 100)
      })
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const elements = entry.target.querySelectorAll('.animate-on-scroll')
            elements.forEach((el, index) => {
              setTimeout(() => {
                el.classList.add('opacity-100', 'translate-y-0', 'translate-x-0')
                el.classList.remove('opacity-0', 'translate-y-8', 'translate-x-8')
              }, index * 150)
            })
          }
        })
      },
      { threshold: 0.1 }
    )

    // Fallback: animate all elements after 2 seconds if observer doesn't work
    const fallbackTimer = setTimeout(animateElements, 2000)

    // Observe all sections with animate-on-scroll elements
    const sections = document.querySelectorAll('section')
    sections.forEach(section => {
      if (section.querySelector('.animate-on-scroll')) {
        observer.observe(section)
      }
    })

    return () => {
      observer.disconnect()
      clearTimeout(fallbackTimer)
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setFormData(prev => ({ ...prev, resume: file }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert("Application submitted successfully! We'll get back to you soon.")
    setFormData({
      name: "",
      email: "",
      phone: "",
      position: "",
      experience: "",
      coverLetter: "",
      resume: null
    })
    setIsSubmitting(false)
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-burgundy-600 via-burgundy-700 to-burgundy-800 text-white py-24 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-yellow-300/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <Users size={16} className="mr-2" />
              Join Our Team
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Build Your <span className="text-yellow-300">Career</span> with Us
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto mb-8">
              Join Dwelling Desire and be part of Ahmedabad's leading real estate company. 
              Grow your career while helping people find their dream homes.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg"
                className="bg-white text-burgundy-600 hover:bg-white/90 px-8 py-3 rounded-full font-semibold"
              >
                <Briefcase size={20} className="mr-2" />
                View Open Positions
              </Button>
              <Button 
                size="lg"
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-full font-semibold"
              >
                <Heart size={20} className="mr-2" />
                Learn About Culture
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Culture Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
              <Heart size={16} className="mr-2" />
              Our Culture
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose <span className="text-burgundy-600">Dwelling Desire</span>?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe in creating an environment where talent thrives, innovation flourishes, 
              and every team member can achieve their full potential.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: TrendingUp,
                title: "Growth Focused",
                description: "Continuous learning opportunities and clear career progression paths for all team members."
              },
              {
                icon: Award,
                title: "Recognition",
                description: "We celebrate achievements and reward excellence with competitive compensation and bonuses."
              },
              {
                icon: Users,
                title: "Team Spirit",
                description: "Collaborative work environment where every voice matters and teamwork drives success."
              },
              {
                icon: Target,
                title: "Innovation",
                description: "Embrace new technologies and creative solutions to stay ahead in the real estate market."
              }
            ].map((value, index) => {
              const IconComponent = value.icon
              return (
                <Card
                  key={index}
                  className="animate-on-scroll border-0 shadow-lg hover:shadow-xl transition-all duration-300 group"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-burgundy-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-burgundy-200 transition-colors">
                      <IconComponent className="w-8 h-8 text-burgundy-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{value.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Job Listings Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
              <Briefcase size={16} className="mr-2" />
              Open Positions
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Current <span className="text-burgundy-600">Opportunities</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore exciting career opportunities and find the perfect role to grow your career with us.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {[
              {
                title: "Senior Property Consultant",
                department: "Sales",
                location: "Prahlad Nagar, Ahmedabad",
                type: "Full-time",
                experience: "3-5 years",
                salary: "₹5-8 LPA + Incentives",
                description: "Lead property sales, client relationships, and market analysis. Perfect for experienced real estate professionals.",
                requirements: ["3+ years real estate experience", "Strong communication skills", "Local market knowledge", "Sales target achievement"]
              },
              {
                title: "Digital Marketing Specialist",
                department: "Marketing",
                location: "Prahlad Nagar, Ahmedabad",
                type: "Full-time",
                experience: "2-4 years",
                salary: "₹4-6 LPA",
                description: "Drive digital marketing campaigns, social media strategy, and online lead generation for our real estate projects.",
                requirements: ["Digital marketing experience", "Social media expertise", "Content creation skills", "Analytics knowledge"]
              },
              {
                title: "Property Manager",
                department: "Operations",
                location: "Ahmedabad",
                type: "Full-time",
                experience: "2-3 years",
                salary: "₹3.5-5 LPA",
                description: "Manage property portfolios, tenant relations, and ensure smooth operations of residential and commercial properties.",
                requirements: ["Property management experience", "Customer service skills", "Problem-solving abilities", "Local area knowledge"]
              },
              {
                title: "Business Development Executive",
                department: "Business Development",
                location: "Ahmedabad",
                type: "Full-time",
                experience: "1-3 years",
                salary: "₹3-4.5 LPA + Incentives",
                description: "Identify new business opportunities, build partnerships, and expand our market presence in Gujarat.",
                requirements: ["Business development experience", "Networking skills", "Market research abilities", "Relationship building"]
              }
            ].map((job, index) => (
              <Card
                key={index}
                className="animate-on-scroll border-0 shadow-lg hover:shadow-xl transition-all duration-300 group"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <CardContent className="p-8">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-burgundy-600 transition-colors">
                        {job.title}
                      </h3>
                      <div className="flex flex-wrap gap-2 mb-4">
                        <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600">
                          {job.department}
                        </Badge>
                        <Badge variant="outline" className="border-gray-300">
                          {job.type}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-gray-600">
                      <MapPin size={16} className="mr-2 text-burgundy-600" />
                      {job.location}
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Clock size={16} className="mr-2 text-burgundy-600" />
                      {job.experience} experience
                    </div>
                    <div className="flex items-center text-gray-600">
                      <DollarSign size={16} className="mr-2 text-burgundy-600" />
                      {job.salary}
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6 leading-relaxed">{job.description}</p>

                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Requirements:</h4>
                    <ul className="space-y-2">
                      {job.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="flex items-center text-gray-600">
                          <div className="w-2 h-2 bg-burgundy-600 rounded-full mr-3"></div>
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Button
                    className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white rounded-xl"
                    onClick={() => {
                      setFormData(prev => ({ ...prev, position: job.title }))
                      document.getElementById('application-form')?.scrollIntoView({ behavior: 'smooth' })
                    }}
                  >
                    <Send size={16} className="mr-2" />
                    Apply Now
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
              <Gift size={16} className="mr-2" />
              Benefits & Perks
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              What We <span className="text-burgundy-600">Offer</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe in taking care of our team with comprehensive benefits and exciting perks.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Shield,
                title: "Health Insurance",
                description: "Comprehensive medical coverage for you and your family with top healthcare providers."
              },
              {
                icon: GraduationCap,
                title: "Learning & Development",
                description: "Professional development programs, certifications, and skill enhancement opportunities."
              },
              {
                icon: Car,
                title: "Transportation",
                description: "Travel allowance and flexible commuting options to make your journey comfortable."
              },
              {
                icon: Coffee,
                title: "Work-Life Balance",
                description: "Flexible working hours, team outings, and a supportive work environment."
              },
              {
                icon: Plane,
                title: "Paid Time Off",
                description: "Generous vacation days, sick leave, and festival holidays to recharge and relax."
              },
              {
                icon: TrendingUp,
                title: "Performance Bonuses",
                description: "Attractive incentive structure and performance-based rewards for exceptional work."
              }
            ].map((benefit, index) => {
              const IconComponent = benefit.icon
              return (
                <Card
                  key={index}
                  className="animate-on-scroll border-0 shadow-lg hover:shadow-xl transition-all duration-300 group text-center"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <CardContent className="p-8">
                    <div className="w-16 h-16 bg-burgundy-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-burgundy-200 transition-colors">
                      <IconComponent className="w-8 h-8 text-burgundy-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{benefit.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Employee Testimonials */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
              <Star size={16} className="mr-2" />
              Employee Stories
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              What Our <span className="text-burgundy-600">Team Says</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Hear from our team members about their experience working at Dwelling Desire.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Priya Sharma",
                position: "Senior Property Consultant",
                image: "/images/team/priya.jpg",
                testimonial: "Working at Dwelling Desire has been an incredible journey. The supportive environment and growth opportunities have helped me achieve my career goals while making a real impact in people's lives.",
                rating: 5
              },
              {
                name: "Rajesh Patel",
                position: "Marketing Manager",
                image: "/images/team/rajesh.jpg",
                testimonial: "The company culture here is amazing. Leadership truly cares about employee development, and the collaborative atmosphere makes every day enjoyable and productive.",
                rating: 5
              },
              {
                name: "Sneha Mehta",
                position: "Property Manager",
                image: "/images/team/sneha.jpg",
                testimonial: "I've grown both professionally and personally at Dwelling Desire. The learning opportunities and supportive colleagues have made this the best workplace I've ever been part of.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card
                key={index}
                className="animate-on-scroll border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-burgundy-100 rounded-full flex items-center justify-center mr-4">
                      <Users className="w-8 h-8 text-burgundy-600" />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900">{testimonial.name}</h3>
                      <p className="text-burgundy-600 text-sm">{testimonial.position}</p>
                    </div>
                  </div>

                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  <p className="text-gray-600 leading-relaxed italic">
                    "{testimonial.testimonial}"
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Application Form */}
      <section id="application-form" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                <Send size={16} className="mr-2" />
                Apply Now
              </Badge>
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Ready to <span className="text-burgundy-600">Join Us</span>?
              </h2>
              <p className="text-xl text-gray-600">
                Submit your application and take the first step towards an exciting career with Dwelling Desire.
              </p>
            </div>

            <Card className="border-0 shadow-xl bg-white">
              <CardContent className="p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Full Name</label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter your full name"
                        className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Email Address</label>
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email"
                        className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Phone Number</label>
                      <Input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter your phone number"
                        className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Position Applied For</label>
                      <Input
                        type="text"
                        name="position"
                        value={formData.position}
                        onChange={handleInputChange}
                        placeholder="Which position interests you?"
                        className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">Years of Experience</label>
                    <Input
                      type="text"
                      name="experience"
                      value={formData.experience}
                      onChange={handleInputChange}
                      placeholder="e.g., 3 years in real estate sales"
                      className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">Cover Letter</label>
                    <Textarea
                      name="coverLetter"
                      value={formData.coverLetter}
                      onChange={handleInputChange}
                      placeholder="Tell us why you want to join Dwelling Desire and what makes you the perfect fit..."
                      rows={5}
                      className="rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0 resize-none"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">Resume/CV</label>
                    <div className="relative">
                      <Input
                        type="file"
                        name="resume"
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx"
                        className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-burgundy-50 file:text-burgundy-700 hover:file:bg-burgundy-100"
                        required
                      />
                      <Upload className="absolute right-3 top-3 w-6 h-6 text-gray-400" />
                    </div>
                    <p className="text-sm text-gray-500">Accepted formats: PDF, DOC, DOCX (Max 5MB)</p>
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white h-12 rounded-xl font-semibold text-lg"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Submitting Application...
                      </>
                    ) : (
                      <>
                        <Send size={20} className="mr-2" />
                        Submit Application
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}

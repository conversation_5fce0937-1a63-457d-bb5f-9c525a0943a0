"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Phone, MessageCircle } from "lucide-react"
import { Home, Building2, Calculator, Key, FileText, TrendingUp, Handshake, Scale, PieChart } from "lucide-react"

export default function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  
  // Check if current page is contact page or home page
  const isContactPage = pathname === '/contact'
  const isHomePage = pathname === '/'

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Dynamic text colors based on page and scroll state
  const getTextColor = () => {
    if (isContactPage && !isScrolled) {
      return "text-white hover:text-white"
    }
    return "text-gray-700 hover:text-burgundy-600"
  }

  // Dynamic hover background based on page and scroll state
  const getHoverBg = () => {
    if ((isContactPage && !isScrolled) || (isHomePage && !isScrolled)) {
      return ""
    }
    return "hover:bg-burgundy-50"
  }

  // Dynamic mobile hamburger colors
  const getHamburgerColor = () => {
    if (isContactPage && !isScrolled) {
      return "bg-white"
    }
    return "bg-gray-600"
  }

  const servicesItems = [
    { title: "Buy", href: "/services/buy", description: "Find your dream property" },
    { title: "Rent", href: "/services/rent", description: "Rental properties" },
    { title: "Lease", href: "/services/lease", description: "Commercial leasing" },
    { title: "Pre-lease", href: "/services/pre-lease", description: "Pre-launch properties" },
    { title: "Sell", href: "/services/sell", description: "Sell your property" },
    { title: "Invest", href: "/services/invest", description: "Investment opportunities" },
  ]

  const projectsItems = [
    { title: "New Projects", href: "/projects/new", description: "Latest developments" },
    { title: "Resale Properties", href: "/projects/resale", description: "Ready to move" },
  ]

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <Image
              src="/images/logo.png"
              alt="Dwelling Desire"
              width={180}
              height={60}
              className={`h-20 w-auto transition-all duration-300 group-hover:scale-105 ${
                isContactPage && !isScrolled ? 'brightness-0 invert' : ''
              }`}
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center">
            <nav className="flex items-center space-x-1">
              {/* Home */}
              <Link
                href="/"
                className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Home
                <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
              </Link>

              {/* About Us */}
              <Link
                href="/about"
                className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                About Us
                <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
              </Link>

              {/* Services Dropdown */}
              <div className="relative group">
                <button className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} flex items-center space-x-1 relative`}>
                  <span>Services</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
                </button>

                {/* Services Dropdown Menu */}
                <div className="absolute top-full left-0 mt-2 w-96 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-[60] backdrop-blur-sm">
                  <div className="p-3">
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        { title: "Buy", desc: "Find your dream property", icon: Home, href: "/services/buy" },
                        { title: "Rent", desc: "Rental properties", icon: Key, href: "/services/rent" },
                        { title: "Lease", desc: "Commercial leasing", icon: Building2, href: "/services/lease" },
                        {
                          title: "Pre-lease",
                          desc: "Pre-launch properties",
                          icon: FileText,
                          href: "/services/pre-lease",
                        },
                        { title: "Sell", desc: "Sell your property", icon: Handshake, href: "/services/sell" },
                        {
                          title: "Invest",
                          desc: "Investment opportunities",
                          icon: TrendingUp,
                          href: "/services/invest",
                        },
                      ].map((item) => {
                        const IconComponent = item.icon
                        return (
                          <Link
                            key={item.title}
                            href={item.href}
                            className="flex items-start space-x-3 p-4 rounded-xl hover:bg-burgundy-50 transition-all duration-300 group/item"
                          >
                            <div className="w-10 h-10 bg-burgundy-100 rounded-full flex items-center justify-center group-hover/item:bg-burgundy-200 transition-colors flex-shrink-0">
                              <IconComponent className="w-4 h-4 text-burgundy-600" />
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900 group-hover/item:text-burgundy-600 transition-colors">
                                {item.title}
                              </div>
                              <div className="text-xs text-gray-500 mt-0.5">{item.desc}</div>
                            </div>
                          </Link>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </div>

              {/* Projects Dropdown */}
              <div className="relative group">
                <button className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} flex items-center space-x-1 relative`}>
                  <span>Projects</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
                </button>

                {/* Projects Dropdown Menu */}
                <div className="absolute top-full left-0 mt-2 w-72 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-[60] backdrop-blur-sm">
                  <div className="p-2">
                    {[
                      { title: "New Projects", desc: "Latest developments", icon: Building2, href: "/projects/new" },
                      { title: "Resale Properties", desc: "Ready to move", icon: Home, href: "/projects/resale" },
                    ].map((item) => {
                      const IconComponent = item.icon
                      return (
                        <Link
                          key={item.title}
                          href={item.href}
                          className="flex items-start space-x-3 p-4 rounded-xl hover:bg-burgundy-50 transition-all duration-300 group/item"
                        >
                          <div className="w-10 h-10 bg-burgundy-100 rounded-full flex items-center justify-center group-hover/item:bg-burgundy-200 transition-colors">
                            <IconComponent className="w-4 h-4 text-burgundy-600" />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900 group-hover/item:text-burgundy-600 transition-colors">
                              {item.title}
                            </div>
                            <div className="text-sm text-gray-500 mt-1">{item.desc}</div>
                          </div>
                        </Link>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* Insights */}
              <Link
                href="/insights"
                className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Insights
                <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
              </Link>

              {/* Calculator Dropdown */}
              <div className="relative group">
                <button className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} flex items-center space-x-1 relative`}>
                  <span>Calculator</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
                </button>

                {/* Calculator Dropdown Menu */}
                <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-[60] backdrop-blur-sm">
                  <div className="p-2">
                    {[
                      {
                        title: "EMI Calculator",
                        desc: "Calculate loan EMI",
                        icon: Calculator,
                        href: "/calculator/emi",
                      },
                      { title: "Rent vs Buy", desc: "Compare options", icon: Scale, href: "/calculator/rent-vs-buy" },
                      { title: "ROI Calculator", desc: "Investment returns", icon: PieChart, href: "/calculator/roi" },
                    ].map((item) => {
                      const IconComponent = item.icon
                      return (
                        <Link
                          key={item.title}
                          href={item.href}
                          className="flex items-start space-x-3 p-4 rounded-xl hover:bg-burgundy-50 transition-all duration-300 group/item"
                        >
                          <div className="w-10 h-10 bg-burgundy-100 rounded-full flex items-center justify-center group-hover/item:bg-burgundy-200 transition-colors">
                            <IconComponent className="w-4 h-4 text-burgundy-600" />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900 group-hover/item:text-burgundy-600 transition-colors">
                              {item.title}
                            </div>
                            <div className="text-sm text-gray-500 mt-1">{item.desc}</div>
                          </div>
                        </Link>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* Career */}
              <Link
                href="/career"
                className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Career
                <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${isContactPage && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
              </Link>
            </nav>

            {/* CTA Button */}
            <div className="ml-8">
              <Button
                asChild
                className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 hover:from-burgundy-700 hover:to-burgundy-800 text-white px-6 py-2.5 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <Link href="/contact" className="flex items-center space-x-2">
                  <span>Let's Talk</span>
                  <MessageCircle size={16} />
                </Link>
              </Button>
            </div>
          </div>

          {/* Mobile Menu Button */}
                      <button
              className={`lg:hidden p-2 rounded-lg transition-colors duration-300 ${
                isContactPage && !isScrolled ? 'hover:bg-white/20' : 'hover:bg-gray-100'
              }`}
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <div className="relative w-6 h-6">
              <span
                className={`absolute block w-6 h-0.5 ${getHamburgerColor()} transition-all duration-300 ${isMobileMenuOpen ? "rotate-45 top-3" : "top-1"}`}
              ></span>
              <span
                className={`absolute block w-6 h-0.5 ${getHamburgerColor()} transition-all duration-300 top-3 ${isMobileMenuOpen ? "opacity-0" : "opacity-100"}`}
              ></span>
              <span
                className={`absolute block w-6 h-0.5 ${getHamburgerColor()} transition-all duration-300 ${isMobileMenuOpen ? "-rotate-45 top-3" : "top-5"}`}
              ></span>
            </div>
          </button>
        </div>

        {/* Enhanced Mobile Menu */}
        <div
          className={`lg:hidden transition-all duration-300 overflow-hidden ${isMobileMenuOpen ? "max-h-[80vh] opacity-100" : "max-h-0 opacity-0"}`}
        >
          <div className="bg-white/95 backdrop-blur-md border-t border-gray-100 py-4 max-h-[80vh] overflow-y-auto">
            <nav className="flex flex-col space-y-2">
              <Link
                href="/"
                className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg mx-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>

              <Link
                href="/about"
                className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg mx-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                About Us
              </Link>

              {/* Mobile Services - Collapsible */}
              <div className="mx-2">
                <details className="group">
                  <summary className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg cursor-pointer flex items-center justify-between">
                    <span>Services</span>
                    <svg
                      className="w-4 h-4 transition-transform duration-300 group-open:rotate-180"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1">
                    {[
                      { title: "Buy", href: "/services/buy", icon: Home },
                      { title: "Rent", href: "/services/rent", icon: Key },
                      { title: "Lease", href: "/services/lease", icon: Building2 },
                      { title: "Pre-lease", href: "/services/pre-lease", icon: FileText },
                      { title: "Sell", href: "/services/sell", icon: Handshake },
                      { title: "Invest", href: "/services/invest", icon: TrendingUp },
                    ].map((item) => {
                      const IconComponent = item.icon
                      return (
                        <Link
                          key={item.title}
                          href={item.href}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-600 hover:text-burgundy-600 hover:bg-burgundy-50 rounded-lg transition-all duration-300"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <div className="w-8 h-8 bg-burgundy-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <IconComponent size={16} className="text-burgundy-600" />
                          </div>
                          <span>{item.title}</span>
                        </Link>
                      )
                    })}
                  </div>
                </details>
              </div>

              {/* Mobile Projects - Collapsible */}
              <div className="mx-2">
                <details className="group">
                  <summary className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg cursor-pointer flex items-center justify-between">
                    <span>Projects</span>
                    <svg
                      className="w-4 h-4 transition-transform duration-300 group-open:rotate-180"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1">
                    {[
                      { title: "New Projects", href: "/projects/new", icon: Building2 },
                      { title: "Resale Properties", href: "/projects/resale", icon: Home },
                    ].map((item) => {
                      const IconComponent = item.icon
                      return (
                        <Link
                          key={item.title}
                          href={item.href}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-600 hover:text-burgundy-600 hover:bg-burgundy-50 rounded-lg transition-all duration-300"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <div className="w-8 h-8 bg-burgundy-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <IconComponent size={16} className="text-burgundy-600" />
                          </div>
                          <span>{item.title}</span>
                        </Link>
                      )
                    })}
                  </div>
                </details>
              </div>

              <Link
                href="/insights"
                className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg mx-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Insights
              </Link>

              {/* Mobile Calculator - Collapsible */}
              <div className="mx-2">
                <details className="group">
                  <summary className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg cursor-pointer flex items-center justify-between">
                    <span>Calculator</span>
                    <svg
                      className="w-4 h-4 transition-transform duration-300 group-open:rotate-180"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1">
                    {[
                      { title: "EMI Calculator", href: "/calculator/emi", icon: Calculator },
                      { title: "Rent vs Buy", href: "/calculator/rent-vs-buy", icon: Scale },
                      { title: "ROI Calculator", href: "/calculator/roi", icon: PieChart },
                    ].map((item) => {
                      const IconComponent = item.icon
                      return (
                        <Link
                          key={item.title}
                          href={item.href}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-600 hover:text-burgundy-600 hover:bg-burgundy-50 rounded-lg transition-all duration-300"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <div className="w-8 h-8 bg-burgundy-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <IconComponent size={16} className="text-burgundy-600" />
                          </div>
                          <span>{item.title}</span>
                        </Link>
                      )
                    })}
                  </div>
                </details>
              </div>

              <Link
                href="/career"
                className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg mx-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Career
              </Link>

              <div className="px-4 pt-4">
                <Button
                  asChild
                  className="w-full bg-gradient-to-r from-burgundy-600 to-burgundy-700 hover:from-burgundy-700 hover:to-burgundy-800 text-white rounded-full font-semibold"
                >
                  <Link href="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                    Let's Talk
                  </Link>
                </Button>
              </div>
            </nav>
          </div>
        </div>
      </div>

    </nav>
  )
}

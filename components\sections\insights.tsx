"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, ArrowRight, BookOpen, User, TrendingUp } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const insights = [
  {
    id: 1,
    title: "Real Estate Market Trends in Ahmedabad 2025: What Buyers, Investors & Builders Need to Know",
    excerpt:
      "Ahmedabad is experiencing a transformative shift in its real estate market. With new infrastructure projects, growing IT presence, and increasing demand for smart housing, 2025 is shaping up to be a defining year.",
    image: "/images/insights/1.webp",
    category: "Market Analysis",
    author: "Dwelling Desire Team",
    date: "2024-07-15",
    readTime: "8 min read",
    featured: true,
  },
  {
    id: 2,
    title: "🏘️ Top 10 Localities for Property Investment in Ahmedabad 2025",
    excerpt: "Explore the most promising areas in Ahmedabad for real estate investment with high growth potential. From emerging micro-markets to established premium zones.",
    image: "/images/insights/2.webp",
    category: "Investment",
    author: "<PERSON><PERSON>",
    date: "2024-06-10",
    readTime: "10 min",
    featured: false,
  },
  {
    id: 3,
    title: "🏠 Complete Home Buying Guide for First-Time Buyers in Ahmedabad 2025",
    excerpt: "A comprehensive step-by-step guide covering everything first-time home buyers need to know about purchasing property in Ahmedabad. From financial planning to legal documentation.",
    image: "/images/insights/3.webp",
    category: "Guide",
    author: "Priya Sharma",
    date: "2024-06-05",
    readTime: "12 min",
    featured: false,
  },
  {
    id: 4,
    title: "💰 Real Estate Investment vs. Other Investment Options: Complete Analysis 2025",
    excerpt: "Compare real estate investment with stocks, bonds, and other investment vehicles to make informed decisions. Understand risk, returns, and liquidity factors.",
    image: "/images/insights/4.webp",
    category: "Investment",
    author: "Kiran Shah",
    date: "2024-04-15",
    readTime: "12 min",
    featured: false,
  },
]

export default function Insights() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20 animate-on-scroll opacity-0 translate-y-8">
          <div className="inline-flex items-center space-x-2 bg-white rounded-full px-6 py-3 text-sm font-medium mb-6 shadow-sm border">
            <BookOpen size={16} className="text-burgundy-600" />
            <span className="text-gray-700">Latest Insights</span>
          </div>

          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Real Estate <span className="text-burgundy-600">Insights</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Stay informed with expert insights and market trends
          </p>
        </div>

        {/* Featured Article */}
        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200 mb-16">
          <Card className="overflow-hidden border-0 shadow-xl bg-white">
            <div className="grid lg:grid-cols-5 gap-0">
              <div className="lg:col-span-3 p-12">
                <Badge className="bg-burgundy-100 text-burgundy-700 hover:bg-burgundy-100 mb-6 px-4 py-2">
                  Featured
                </Badge>
                <h3 className="text-4xl font-bold text-gray-900 mb-6 leading-tight">{insights[0].title}</h3>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">{insights[0].excerpt}</p>

                <div className="flex items-center space-x-6 mb-8 text-sm text-gray-500">
                  <div className="flex items-center space-x-2">
                    <User size={16} />
                    <span>{insights[0].author}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar size={16} />
                    <span suppressHydrationWarning>{new Date(insights[0].date).toLocaleDateString('en-IN')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock size={16} />
                    <span>{insights[0].readTime}</span>
                  </div>
                </div>

                <Button
                  className="bg-burgundy-600 hover:bg-burgundy-700 text-white px-8 py-3 rounded-full font-semibold group"
                  asChild
                >
                  <Link href={`/insights/${insights[0].id}`}>
                    Read Article
                    <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={16} />
                  </Link>
                </Button>
              </div>

              <div className="lg:col-span-2 relative min-h-[400px]">
                <Image
                  src={insights[0].image || "/placeholder.svg"}
                  alt={insights[0].title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </Card>
        </div>

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {insights.slice(1).map((article, index) => (
            <Card
              key={article.id}
              className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-lg transition-all duration-300 border-0 bg-white overflow-hidden`}
              style={{ animationDelay: `${(index + 1) * 150}ms` }}
            >
              <div className="relative overflow-hidden">
                <Image
                  src={article.image || "/placeholder.svg"}
                  alt={article.title}
                  width={400}
                  height={250}
                  className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute top-4 left-4">
                  <Badge className="bg-white/90 text-gray-700 hover:bg-white/90 text-xs font-medium">
                    {article.category}
                  </Badge>
                </div>
              </div>

              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4 leading-tight group-hover:text-burgundy-600 transition-colors">
                  {article.title}
                </h3>

                <p className="text-gray-600 mb-6 leading-relaxed">{article.excerpt}</p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                  <div className="flex items-center space-x-2">
                    <User size={14} />
                    <span>{article.author}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span suppressHydrationWarning>{new Date(article.date).toLocaleDateString('en-IN')}</span>
                    <span>•</span>
                    <span>{article.readTime}</span>
                  </div>
                </div>

                <Button
                  variant="ghost"
                  className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 p-0 h-auto font-semibold group/btn"
                  asChild
                >
                  <Link href={`/insights/${article.id}`}>
                    Read More
                    <ArrowRight className="ml-2 group-hover/btn:translate-x-1 transition-transform" size={14} />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Newsletter Section */}
        <div className="animate-on-scroll opacity-0 translate-y-8 delay-600">
          <Card className="border-0 bg-white shadow-lg">
            
          </Card>
        </div>

        {/* View All CTA */}
        <div className="text-center mt-16 animate-on-scroll opacity-0 translate-y-8 delay-800">
          <Button
            size="lg"
            variant="outline"
            className="border-2 border-burgundy-600 text-burgundy-600 hover:bg-burgundy-600 hover:text-white px-10 py-4 rounded-full font-semibold transition-all duration-300 group bg-white"
            asChild
          >
            <Link href="/insights">
              View All Articles
              <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
